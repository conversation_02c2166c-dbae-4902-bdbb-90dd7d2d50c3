#!/usr/bin/env python3
"""
Flask Web Application for DeemixDownloader
Provides a web interface to run the DeemixDownloader.py script with real-time progress updates.
"""

import os
import sys
import subprocess
import threading
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify
from flask_socketio import Socket<PERSON>, emit
import signal
import queue
import json

app = Flask(__name__)
app.config['SECRET_KEY'] = 'deemix-downloader-secret-key'
socketio = SocketIO(app, cors_allowed_origins="*")

# Global variables to track script execution
current_process = None
process_thread = None
is_running = False
output_queue = queue.Queue()

class ScriptRunner:
    def __init__(self):
        self.process = None
        self.is_running = False
        
    def run_script(self):
        """Run the DeemixDownloader.py script and capture output"""
        global is_running, current_process
        
        try:
            is_running = True
            socketio.emit('script_started', {'message': 'Starting DeemixDownloader...'})
            
            # Get the directory where this web app is located
            script_dir = os.path.dirname(os.path.abspath(__file__))
            deemix_script = os.path.join(script_dir, 'DeemixDownloader.py')
            
            if not os.path.exists(deemix_script):
                error_msg = f"DeemixDownloader.py not found at {deemix_script}"
                socketio.emit('script_error', {'message': error_msg})
                return
            
            # Start the subprocess
            self.process = subprocess.Popen(
                [sys.executable, deemix_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1,
                cwd=script_dir
            )
            
            current_process = self.process
            
            # Read output line by line and emit to frontend
            for line in iter(self.process.stdout.readline, ''):
                if line:
                    line = line.strip()
                    if line:
                        socketio.emit('script_output', {
                            'message': line,
                            'timestamp': datetime.now().strftime('%H:%M:%S')
                        })
                        
                # Check if process was terminated
                if self.process.poll() is not None:
                    break
            
            # Wait for process to complete
            return_code = self.process.wait()
            
            if return_code == 0:
                socketio.emit('script_completed', {
                    'message': 'DeemixDownloader completed successfully!',
                    'return_code': return_code
                })
            else:
                socketio.emit('script_error', {
                    'message': f'DeemixDownloader exited with code {return_code}',
                    'return_code': return_code
                })
                
        except Exception as e:
            socketio.emit('script_error', {'message': f'Error running script: {str(e)}'})
        finally:
            is_running = False
            current_process = None
            self.process = None

script_runner = ScriptRunner()

@app.route('/')
def index():
    """Serve the main web interface"""
    return render_template('index.html')

@app.route('/status')
def status():
    """Get current script status"""
    return jsonify({
        'is_running': is_running,
        'has_process': current_process is not None
    })

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    emit('connected', {'message': 'Connected to DeemixDownloader Web Interface'})
    emit('status_update', {'is_running': is_running})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    print('Client disconnected')

@socketio.on('start_script')
def handle_start_script():
    """Handle request to start the DeemixDownloader script"""
    global process_thread, is_running
    
    if is_running:
        emit('script_error', {'message': 'Script is already running!'})
        return
    
    # Start script in a separate thread
    process_thread = threading.Thread(target=script_runner.run_script)
    process_thread.daemon = True
    process_thread.start()
    
    emit('script_starting', {'message': 'Starting DeemixDownloader script...'})

@socketio.on('stop_script')
def handle_stop_script():
    """Handle request to stop the running script"""
    global current_process, is_running
    
    if not is_running or current_process is None:
        emit('script_error', {'message': 'No script is currently running!'})
        return
    
    try:
        # Terminate the process
        current_process.terminate()
        
        # Wait a bit for graceful termination
        time.sleep(2)
        
        # Force kill if still running
        if current_process.poll() is None:
            current_process.kill()
        
        is_running = False
        current_process = None
        
        emit('script_stopped', {'message': 'Script has been stopped.'})
        
    except Exception as e:
        emit('script_error', {'message': f'Error stopping script: {str(e)}'})

def signal_handler(sig, frame):
    """Handle shutdown signals"""
    global current_process
    print('Shutting down...')
    if current_process:
        current_process.terminate()
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    templates_dir = os.path.join(os.path.dirname(__file__), 'templates')
    if not os.path.exists(templates_dir):
        os.makedirs(templates_dir)
    
    # Create static directory if it doesn't exist
    static_dir = os.path.join(os.path.dirname(__file__), 'static')
    if not os.path.exists(static_dir):
        os.makedirs(static_dir)
    
    print("Starting DeemixDownloader Web Interface...")
    print("Access the interface at: http://localhost:10000")
    
    # Run the Flask-SocketIO app
    socketio.run(app, host='0.0.0.0', port=10000, debug=False)
