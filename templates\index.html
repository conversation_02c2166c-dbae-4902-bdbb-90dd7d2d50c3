<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeemixDownloader Web Interface</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .controls {
            padding: 30px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }

        .btn {
            padding: 15px 30px;
            font-size: 1.1em;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }

        .btn-danger:hover:not(:disabled) {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(255, 107, 107, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .status {
            padding: 20px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #eee;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 10px;
            animation: pulse 2s infinite;
        }

        .status-idle {
            background: #6c757d;
        }

        .status-running {
            background: #28a745;
        }

        .status-error {
            background: #dc3545;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .console {
            height: 500px;
            background: #1e1e1e;
            color: #d4d4d4;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-y: auto;
            padding: 20px;
            border-radius: 0 0 15px 15px;
        }

        .console-line {
            margin-bottom: 5px;
            word-wrap: break-word;
        }

        .console-timestamp {
            color: #569cd6;
            margin-right: 10px;
        }

        .console-message {
            color: #d4d4d4;
        }

        .console-error {
            color: #f44747;
        }

        .console-success {
            color: #4ec9b0;
        }

        .console-warning {
            color: #ffcc02;
        }

        .console::-webkit-scrollbar {
            width: 8px;
        }

        .console::-webkit-scrollbar-track {
            background: #2d2d30;
        }

        .console::-webkit-scrollbar-thumb {
            background: #464647;
            border-radius: 4px;
        }

        .console::-webkit-scrollbar-thumb:hover {
            background: #5a5a5c;
        }

        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 5px;
            color: white;
            font-weight: bold;
            z-index: 1000;
        }

        .connected {
            background: #28a745;
        }

        .disconnected {
            background: #dc3545;
        }
    </style>
</head>
<body>
    <div id="connectionStatus" class="connection-status disconnected">Disconnected</div>
    
    <div class="container">
        <div class="header">
            <h1>🎵 DeemixDownloader</h1>
            <p>Web Interface for Music Download Tool</p>
        </div>

        <div class="controls">
            <button id="startBtn" class="btn btn-primary">▶️ Run Script</button>
            <button id="stopBtn" class="btn btn-danger" disabled>⏹️ Stop Script</button>
        </div>

        <div class="status">
            <span id="statusIndicator" class="status-indicator status-idle"></span>
            <span id="statusText">Ready to start</span>
        </div>

        <div id="console" class="console">
            <div class="console-line">
                <span class="console-timestamp">[SYSTEM]</span>
                <span class="console-message">DeemixDownloader Web Interface initialized. Click "Run Script" to start.</span>
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // DOM elements
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const statusIndicator = document.getElementById('statusIndicator');
        const statusText = document.getElementById('statusText');
        const console = document.getElementById('console');
        const connectionStatus = document.getElementById('connectionStatus');

        // State
        let isRunning = false;

        // Socket event handlers
        socket.on('connect', function() {
            connectionStatus.textContent = 'Connected';
            connectionStatus.className = 'connection-status connected';
            addConsoleMessage('SYSTEM', 'Connected to server', 'success');
        });

        socket.on('disconnect', function() {
            connectionStatus.textContent = 'Disconnected';
            connectionStatus.className = 'connection-status disconnected';
            addConsoleMessage('SYSTEM', 'Disconnected from server', 'error');
        });

        socket.on('connected', function(data) {
            addConsoleMessage('SYSTEM', data.message, 'success');
        });

        socket.on('status_update', function(data) {
            updateStatus(data.is_running);
        });

        socket.on('script_starting', function(data) {
            addConsoleMessage('SYSTEM', data.message, 'warning');
        });

        socket.on('script_started', function(data) {
            updateStatus(true);
            addConsoleMessage('SYSTEM', data.message, 'success');
        });

        socket.on('script_output', function(data) {
            addConsoleMessage(data.timestamp, data.message);
        });

        socket.on('script_completed', function(data) {
            updateStatus(false);
            addConsoleMessage('SYSTEM', data.message, 'success');
        });

        socket.on('script_error', function(data) {
            updateStatus(false);
            addConsoleMessage('ERROR', data.message, 'error');
        });

        socket.on('script_stopped', function(data) {
            updateStatus(false);
            addConsoleMessage('SYSTEM', data.message, 'warning');
        });

        // Button event handlers
        startBtn.addEventListener('click', function() {
            if (!isRunning) {
                socket.emit('start_script');
                addConsoleMessage('SYSTEM', 'Starting DeemixDownloader...', 'warning');
            }
        });

        stopBtn.addEventListener('click', function() {
            if (isRunning) {
                socket.emit('stop_script');
                addConsoleMessage('SYSTEM', 'Stopping script...', 'warning');
            }
        });

        // Helper functions
        function updateStatus(running) {
            isRunning = running;
            
            if (running) {
                startBtn.disabled = true;
                stopBtn.disabled = false;
                statusIndicator.className = 'status-indicator status-running';
                statusText.textContent = 'Script is running...';
            } else {
                startBtn.disabled = false;
                stopBtn.disabled = true;
                statusIndicator.className = 'status-indicator status-idle';
                statusText.textContent = 'Ready to start';
            }
        }

        function addConsoleMessage(timestamp, message, type = 'normal') {
            const line = document.createElement('div');
            line.className = 'console-line';
            
            const timestampSpan = document.createElement('span');
            timestampSpan.className = 'console-timestamp';
            timestampSpan.textContent = `[${timestamp}]`;
            
            const messageSpan = document.createElement('span');
            messageSpan.className = `console-message ${type === 'error' ? 'console-error' : type === 'success' ? 'console-success' : type === 'warning' ? 'console-warning' : ''}`;
            messageSpan.textContent = message;
            
            line.appendChild(timestampSpan);
            line.appendChild(messageSpan);
            console.appendChild(line);
            
            // Auto-scroll to bottom
            console.scrollTop = console.scrollHeight;
        }

        // Initialize
        updateStatus(false);
    </script>
</body>
</html>
