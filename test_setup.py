#!/usr/bin/env python3
"""
Test script to validate the DeemixDownloader setup
"""

import os
import json
import sys

def test_file_structure():
    """Test that all required files are present"""
    print("🔍 Testing file structure...")
    
    required_files = [
        'DeemixDownloader.py',
        'web_app.py',
        'requirements.txt',
        'Dockerfile',
        'docker-compose.yml',
        'templates/index.html',
        'config/config.json'
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ Missing files: {', '.join(missing_files)}")
        return False
    else:
        print("✅ All required files are present")
        return True

def test_config_file():
    """Test that config.json is valid and has correct settings"""
    print("🔍 Testing configuration file...")
    
    try:
        with open('config/config.json', 'r') as f:
            config = json.load(f)
        
        # Check download location
        download_location = config.get('downloadLocation', '')
        if download_location != '/app/downloads':
            print(f"⚠️  Warning: downloadLocation is '{download_location}', should be '/app/downloads' for container")
        else:
            print("✅ Download location is correctly set for container")
        
        # Check other important settings
        required_keys = ['tracknameTemplate', 'createArtistFolder', 'createAlbumFolder']
        for key in required_keys:
            if key not in config:
                print(f"⚠️  Warning: Missing config key: {key}")
            else:
                print(f"✅ Config key '{key}' is present")
        
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ Invalid JSON in config.json: {e}")
        return False
    except Exception as e:
        print(f"❌ Error reading config.json: {e}")
        return False

def test_python_syntax():
    """Test that Python files have valid syntax"""
    print("🔍 Testing Python syntax...")
    
    python_files = ['DeemixDownloader.py', 'web_app.py']
    
    for file_path in python_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                compile(f.read(), file_path, 'exec')
            print(f"✅ {file_path} has valid syntax")
        except SyntaxError as e:
            print(f"❌ Syntax error in {file_path}: {e}")
            return False
        except Exception as e:
            print(f"❌ Error checking {file_path}: {e}")
            return False
    
    return True

def test_html_template():
    """Test that HTML template exists and has basic structure"""
    print("🔍 Testing HTML template...")
    
    try:
        with open('templates/index.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for essential elements
        required_elements = [
            '<!DOCTYPE html>',
            '<title>',
            'socket.io',
            'startBtn',
            'stopBtn',
            'console'
        ]
        
        for element in required_elements:
            if element not in content:
                print(f"⚠️  Warning: HTML template missing '{element}'")
            else:
                print(f"✅ HTML template contains '{element}'")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reading HTML template: {e}")
        return False

def test_directories():
    """Test that required directories exist"""
    print("🔍 Testing directory structure...")
    
    required_dirs = ['templates', 'config', 'downloads']
    
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            print(f"⚠️  Creating missing directory: {dir_path}")
            os.makedirs(dir_path, exist_ok=True)
        else:
            print(f"✅ Directory '{dir_path}' exists")
    
    return True

def main():
    """Run all tests"""
    print("🎵 DeemixDownloader Setup Validation")
    print("=" * 40)
    
    tests = [
        test_file_structure,
        test_config_file,
        test_python_syntax,
        test_html_template,
        test_directories
    ]
    
    all_passed = True
    
    for test in tests:
        try:
            result = test()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            all_passed = False
        print()
    
    print("=" * 40)
    if all_passed:
        print("🎉 All tests passed! Setup looks good.")
        print("\n📋 Next steps:")
        print("1. Ensure Docker is installed")
        print("2. Update Spotify API credentials in DeemixDownloader.py")
        print("3. Run: docker-compose up --build")
        print("4. Access: http://localhost:10000")
    else:
        print("❌ Some tests failed. Please fix the issues above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
