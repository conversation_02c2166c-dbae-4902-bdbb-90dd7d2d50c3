@echo off
echo 🎵 DeemixDownloader Web Interface Setup
echo ======================================

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM Create necessary directories
echo 📁 Creating necessary directories...
if not exist "downloads" mkdir downloads
if not exist "spotify_cache" mkdir spotify_cache

REM Check if config directory exists
if not exist "config" (
    echo ❌ Config directory not found. Please ensure config\config.json exists.
    pause
    exit /b 1
)

REM Check if config.json exists
if not exist "config\config.json" (
    echo ❌ config\config.json not found. Please create the configuration file.
    pause
    exit /b 1
)

echo 🔧 Checking configuration...
REM Note: Windows batch doesn't have easy sed equivalent, so we'll just warn the user
findstr "/mnt/NAS/Music/Deemix" config\config.json >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  Warning: Please update downloadLocation in config\config.json to "/app/downloads"
)

echo 🐳 Building and starting Docker container...
docker-compose up --build -d

echo.
echo ✅ DeemixDownloader Web Interface is starting!
echo.
echo 🌐 Access the web interface at: http://localhost:10000
echo.
echo 📋 Useful commands:
echo    View logs:    docker-compose logs -f
echo    Stop:         docker-compose down
echo    Restart:      docker-compose restart
echo.
echo 📁 Downloads will be saved to: .\downloads\
echo.

REM Wait a moment and check if container is running
timeout /t 5 /nobreak >nul
docker-compose ps | findstr "Up" >nul 2>&1
if %errorlevel% equ 0 (
    echo 🎉 Container is running successfully!
) else (
    echo ❌ Container failed to start. Check logs with: docker-compose logs
)

pause
