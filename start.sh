#!/bin/bash

# DeemixDownloader Web Interface Startup Script

echo "🎵 DeemixDownloader Web Interface Setup"
echo "======================================"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p downloads
mkdir -p spotify_cache

# Check if config directory exists
if [ ! -d "config" ]; then
    echo "❌ Config directory not found. Please ensure config/config.json exists."
    exit 1
fi

# Check if config.json exists
if [ ! -f "config/config.json" ]; then
    echo "❌ config/config.json not found. Please create the configuration file."
    exit 1
fi

# Update download location in config if needed
echo "🔧 Checking configuration..."
if grep -q '"/mnt/NAS/Music/Deemix"' config/config.json; then
    echo "⚠️  Updating download location in config.json for container use..."
    sed -i 's|"/mnt/NAS/Music/Deemix"|"/app/downloads"|g' config/config.json
fi

echo "🐳 Building and starting Docker container..."
docker-compose up --build -d

echo ""
echo "✅ DeemixDownloader Web Interface is starting!"
echo ""
echo "🌐 Access the web interface at: http://localhost:10000"
echo ""
echo "📋 Useful commands:"
echo "   View logs:    docker-compose logs -f"
echo "   Stop:         docker-compose down"
echo "   Restart:      docker-compose restart"
echo ""
echo "📁 Downloads will be saved to: ./downloads/"
echo ""

# Wait a moment and check if container is running
sleep 5
if docker-compose ps | grep -q "Up"; then
    echo "🎉 Container is running successfully!"
else
    echo "❌ Container failed to start. Check logs with: docker-compose logs"
fi
