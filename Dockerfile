# Use Python 3.11 slim image as base
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV DEBIAN_FRONTEND=noninteractive

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    git \
    build-essential \
    ffmpeg \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# Install deemix via npm
RUN npm install -g deemix

# Create directories for the application
RUN mkdir -p /app/downloads /app/config /app/templates /app/static

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application files
COPY DeemixDownloader.py .
COPY web_app.py .
COPY templates/ ./templates/
COPY config/ ./config/

# Create a non-root user for security
RUN useradd -m -u 1000 deemix && \
    chown -R deemix:deemix /app

# Switch to non-root user
USER deemix

# Create volume mount points
VOLUME ["/app/downloads", "/app/config"]

# Expose port 10000 for the web interface
EXPOSE 10000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:10000/status || exit 1

# Default command to run the web application
CMD ["python", "web_app.py"]
