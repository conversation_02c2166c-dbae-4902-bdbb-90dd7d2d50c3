networks:
  deemix-network:
    driver: bridge
services:
  deemix-downloader:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: deemix-downloader-web
    environment:
      - TZ=UTC
      - PYTHONUNBUFFERED=1
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
        - CMD
        - curl
        - '-f'
        - http://localhost:10000/status
      timeout: 10s
    networks:
      - deemix-network
    ports:
      - '10000:10000'
    restart: unless-stopped
    user: '568:568'
    volumes:
      - /mnt/Array/NAS/Music/Deemix:/app/downloads
      - /mnt/Rocket/UltimateMusicDownloader:/app/config
      - /mnt/Rocket/UltimateMusicDownloader/spotify_cache:/app/.cache
version: '3.8'
