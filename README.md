# DeemixDownloader Web Interface

A containerized web interface for the DeemixDownloader Python script that allows you to download music from Spotify playlists and liked songs via a simple web UI.

## Features

- 🎵 **Web-based Interface**: Simple, clean web UI accessible on port 10000
- 🔄 **Real-time Progress**: Live console output showing download progress
- 🐳 **Fully Containerized**: Docker setup with all dependencies included
- 📁 **Volume Mounting**: Persistent downloads and configuration
- 🔍 **Duplicate Detection**: Smart duplicate detection to avoid re-downloading
- 📊 **Statistics**: Detailed download statistics and failed download tracking
- 🎧 **Spotify Integration**: Fetches liked songs and playlists from Spotify

## Prerequisites

- Docker and Docker Compose installed
- Spotify Developer Account (for API credentials)
- Deemix account/configuration

## Quick Start

### 1. Clone or Setup

Ensure you have all the required files in your directory:
```
├── DeemixDownloader.py
├── web_app.py
├── Dockerfile
├── docker-compose.yml
├── requirements.txt
├── templates/
│   └── index.html
└── config/
    └── config.json
```

### 2. Configure Spotify API

Edit the `DeemixDownloader.py` file and update the Spotify API credentials:

```python
SPOTIPY_CLIENT_ID = 'your_spotify_client_id'
SPOTIPY_CLIENT_SECRET = 'your_spotify_client_secret'
SPOTIPY_REDIRECT_URI = 'http://127.0.0.1:8888/callback'
```

To get Spotify API credentials:
1. Go to [Spotify Developer Dashboard](https://developer.spotify.com/dashboard)
2. Create a new app
3. Copy the Client ID and Client Secret
4. Add `http://127.0.0.1:8888/callback` as a redirect URI

### 3. Configure Deemix

Update the `config/config.json` file with your preferred settings:
- Set `downloadLocation` to `/app/downloads` (this will be mounted to your host)
- Configure other preferences as needed

### 4. Build and Run

#### Using Docker Compose (Recommended)

```bash
# Build and start the container
docker-compose up --build

# Or run in detached mode
docker-compose up --build -d
```

#### Using Docker directly

```bash
# Build the image
docker build -t deemix-downloader .

# Run the container
docker run -d \
  --name deemix-downloader-web \
  -p 10000:10000 \
  -v $(pwd)/downloads:/app/downloads \
  -v $(pwd)/config:/app/config \
  deemix-downloader
```

### 5. Access the Web Interface

Open your browser and navigate to:
```
http://localhost:10000
```

## Usage

1. **Access the Interface**: Open http://localhost:10000 in your browser
2. **Start Download**: Click the "▶️ Run Script" button
3. **Monitor Progress**: Watch real-time output in the console area
4. **Stop if Needed**: Use the "⏹️ Stop Script" button to halt execution
5. **Check Downloads**: Downloaded files will appear in the `./downloads` directory

## Directory Structure

```
./
├── downloads/          # Downloaded music files (mounted volume)
├── config/            # Deemix configuration (mounted volume)
├── spotify_cache/     # Spotify authentication cache (mounted volume)
├── failed_downloads.txt # Log of failed downloads
└── [application files]
```

## Configuration

### Deemix Configuration

The `config/config.json` file controls download behavior:
- `downloadLocation`: Where files are saved (use `/app/downloads`)
- `createArtistFolder`: Create folders by artist
- `createAlbumFolder`: Create folders by album
- `maxBitrate`: Audio quality setting
- And many more options...

### Environment Variables

You can customize the container using environment variables in `docker-compose.yml`:

```yaml
environment:
  - TZ=America/New_York  # Set timezone
  - PYTHONUNBUFFERED=1   # Ensure real-time output
```

## Troubleshooting

### Common Issues

1. **Spotify Authentication Fails**
   - Verify your Spotify API credentials
   - Ensure redirect URI is correctly set
   - Check that the Spotify app has the required scopes

2. **Deemix Not Working**
   - Verify deemix is properly configured
   - Check that you have valid Deezer access
   - Ensure config.json is properly formatted

3. **Permission Issues**
   - Check that the downloads directory is writable
   - Verify volume mounts are correct

4. **Web Interface Not Loading**
   - Ensure port 10000 is not blocked
   - Check container logs: `docker-compose logs`

### Viewing Logs

```bash
# View real-time logs
docker-compose logs -f

# View logs for specific service
docker-compose logs deemix-downloader
```

### Debugging

```bash
# Access container shell
docker-compose exec deemix-downloader /bin/bash

# Check container status
docker-compose ps
```

## Security Notes

- The container runs as a non-root user for security
- Only port 10000 is exposed
- Sensitive data (Spotify credentials) should be managed securely
- Consider using environment variables for credentials in production

## Updating

To update the application:

```bash
# Stop the container
docker-compose down

# Rebuild with latest changes
docker-compose up --build
```

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review container logs for error messages
3. Ensure all prerequisites are met
4. Verify configuration files are correct

## License

This project builds upon the original DeemixDownloader script and adds web interface capabilities.
